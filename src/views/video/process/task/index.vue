<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:before>
      <el-button type="primary" icon="el-icon-plus" @click="handleCreateTask">
        新增
      </el-button>
    </template>

    <!-- 状态列自定义渲染 -->
    <!-- <template #table:value3:simple="{ row }">
      <el-tag :type="getStatusTagType(row.value3)">
        {{ row.value3 }}
      </el-tag>
    </template> -->

    <!-- 进度列自定义渲染 -->
    <template #table:value4:simple="{ row }">
      <el-progress
        :percentage="row.value4 || 0"
        :status="getProgressStatus(row.value3)"
        :stroke-width="8"
        :show-text="true"
      />
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button
        v-if="['待执行', '已停止'].includes(row.value3)"
        type="text"
        size="mini"
        @click="handleExecute(row)"
      >
        执行
      </el-button>
      <el-button
        v-if="['运行中'].includes(row.value3)"
        type="text"
        size="mini"
        @click="handleStop(row)"
      >
        停止
      </el-button>
      <el-button
        v-if="['异常', '已完成'].includes(row.value3)"
        type="text"
        size="mini"
        @click="handleReExecute(row)"
      >
        重新执行
      </el-button>
    </template>

    <template #info:before></template>
    <template #after>
      <!-- 任务表单对话框 -->
      <TaskFormDialog
        ref="taskFormDialog"
        @success="handleTaskFormSuccess"
      />
    </template>
  </EleSheet>

</template>

<script>
import request from '@/utils/request.js'
import { taskStatus } from '@/dicts/video/index.js'
import TaskFormDialog from './components/TaskFormDialog.vue'

export default {
  name: 'VideoProcessTask',
  components: {
    TaskFormDialog
  },
  data() {
    return {
      tableType: 'video_processing_task'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '处理任务',
        selection: true,
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          // add: (data) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'post',
          //     data: {
          //       ...data,
          //       type: this.tableType
          //     }
          //   }),
          // edit: (data) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'put',
          //     data: {
          //       ...data,
          //       type: this.tableType
          //     }
          //   }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
          // export: '/system/AutoOsmotic/export'
        },
        model: {
          value1: {
            type: 'text',
            label: '任务名称',
            width: 150,
            showOverflowTooltip: true,
            search: {
              type: 'text',
              placeholder: '请输入任务名称'
            },
            form: {
              type: 'text',
              placeholder: '请输入任务名称',
              rules: [{ required: true, message: '请输入任务名称', trigger: 'blur' }]
            }
          },
          value2: {
            type: 'text',
            label: '模板名称',
            width: 150,
            showOverflowTooltip: true,
            search: {
              type: 'text',
              placeholder: '请输入模板名称'
            },
            form: {
              type: 'text',
              placeholder: '请输入模板名称',
              rules: [{ required: true, message: '请输入模板名称', trigger: 'blur' }]
            }
          },
          value3: {
            type: 'text',
            label: '状态',
            width: 100,
            search: {
              type: 'select',
              placeholder: '请选择状态',
              options: taskStatus
            },
            form: {
              hidden: true,
              type: 'select',
              placeholder: '请选择状态',
              options: taskStatus,
              rules: [{ required: true, message: '请选择状态', trigger: 'change' }],
              value: '待执行'
            }
          },
          value4: {
            type: 'text',
            label: '进度',
            width: 120,
            search: { hidden: true },
            form: {
              type: 'number',
              placeholder: '请输入进度(0-100)',
              min: 0,
              max: 100
            }
          },
          value5: {
            type: 'text',
            label: '创建人',
            width: 100,
            search: { hidden: true },
            form: {
              type: 'text',
              placeholder: '请输入创建人'
            }
          },
          value6: {
            type: 'text',
            label: '创建时间',
            width: 160,
            search: {
              type: 'date-time-range',
              props: {
                type: 'daterange',
                rangeSeparator: '至',
                startPlaceholder: '开始日期',
                endPlaceholder: '结束日期',
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd'
              }
            },
            form: { hidden: true }
          },
          value7: {
            type: 'text',
            label: '视频唯一编号',
            showOverflowTooltip: true,
            search: { hidden: true },
            form: {
              type: 'text',
              placeholder: '请输入视频唯一编号'
            }
          }
        }
      }
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '运行中': 'success',
        '已完成': 'success',
        '已停止': 'info',
        '待执行': 'warning',
        '异常': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 获取进度条状态
    getProgressStatus(taskStatus) {
      if (taskStatus === '异常') return 'exception'
      if (taskStatus === '已执行') return 'success'
      return null
    },

    // 创建新任务
    handleCreateTask() {
      this.$refs.taskFormDialog.open()
    },

    // 导出数据
    async handleExport() {
      try {
        this.$message.info('正在导出数据，请稍候...')
        // TODO: 实现导出功能
        // const params = this.$refs.sheetRef.listParameter()
        // await this.download('/api/process/task/export', params, 'process_tasks.xlsx')
        setTimeout(() => {
          this.$message.success('导出成功！')
        }, 2000)
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请稍后重试')
      }
    },

    // 批量删除
    handleBatchDelete(selected) {
      if (!selected || selected.length === 0) {
        this.$message.warning('请选择要删除的任务')
        return
      }

      this.$modal.confirm(`确认要删除选中的 ${selected.length} 个任务吗？`).then(() => {
        const ids = selected.map(item => item.id).join(',')
        this.$refs.sheetRef.handleRemove(ids.split(','))
      }).catch(() => {})
    },

    // 执行任务
    async handleExecute(row) {
      try {
        await this.$modal.confirm(`确认要执行任务"${row.value1}"吗？`)

        const updateData = {
          ...row,
          value3: '运行中'
        }

        await this.sheetProps.api.edit(updateData)
        this.$modal.msgSuccess('任务已开始执行')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('执行任务失败:', error)
          this.$modal.msgError('执行任务失败')
        }
      }
    },

    // 停止任务
    async handleStop(row) {
      try {
        await this.$modal.confirm(`确认要停止任务"${row.value1}"吗？`)

        const updateData = {
          ...row,
          value3: '已停止'
        }

        await this.sheetProps.api.edit(updateData)
        this.$modal.msgSuccess('任务已停止')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('停止任务失败:', error)
          this.$modal.msgError('停止任务失败')
        }
      }
    },

    // 运维监控
    handleMonitor(row) {
      this.$message.info(`查看任务"${row.value1}"的运维监控信息`)
      // TODO: 实现运维监控功能
    },

    // 重新执行任务
    async handleReExecute(row) {
      try {
        await this.$modal.confirm(`确认要重新执行任务"${row.value1}"吗？`)

        const updateData = {
          ...row,
          value3: '运行中'
        }

        await this.sheetProps.api.edit(updateData)
        this.$modal.msgSuccess('任务已重新执行')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重新执行任务失败:', error)
          this.$modal.msgError('重新执行任务失败')
        }
      }
    },

    // 暂停任务
    handlePause(row) {
      this.$modal.confirm(`确认要暂停任务"${row.value1}"吗？`).then(() => {
        // TODO: 调用暂停接口
        console.log('暂停任务:', row)
        this.$modal.msgSuccess('任务已暂停')
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 取消任务
    handleCancel(row) {
      this.$modal.confirm(`确认要取消任务"${row.value1}"吗？`).then(() => {
        // TODO: 调用取消接口
        console.log('取消任务:', row)
        this.$modal.msgSuccess('任务已取消')
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 开始执行
    handleStart(row) {
      this.$modal.confirm(`确认要开始执行任务"${row.value1}"吗？`).then(() => {
        // TODO: 调用开始执行接口
        console.log('开始执行任务:', row)
        this.$modal.msgSuccess('任务已开始执行')
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 编辑任务
    handleEdit(row) {
      this.$refs.taskFormDialog.open(row)
    },

    // 删除任务
    handleDelete(row) {
      this.$refs.sheetRef.handleRemove(row)
    },

    // 任务表单成功回调
    handleTaskFormSuccess() {
      // 刷新任务列表
      this.$refs.sheetRef.getTableData()
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
